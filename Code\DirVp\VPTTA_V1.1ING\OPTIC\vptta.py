import os
import torch
import numpy as np
import argparse, sys, datetime
from config import Logger
from torch.autograd import Variable
from utils.convert import AdaBN
from utils.memory import Memory
from utils.dgaf_prompt import DGAFPrompt
from utils.image_summary import ImageSummary
from utils.grata_optimizer import GraTaOptimizer
from utils.augmentation import create_augmentation_pipeline
from utils.metrics import calculate_metrics
from networks.ResUnet_TTA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


torch.set_num_threads(1)


class VPTTA:
    def __init__(self, config):
        # Save Log
        time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'VPTTA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)
        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers)
        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # Optimizer
        self.optim = config.optimizer
        self.lr = config.lr
        self.weight_decay = config.weight_decay
        self.momentum = config.momentum
        self.betas = (config.beta1, config.beta2)

        # GPU
        self.device = config.device

        # Warm-up
        self.warm_n = config.warm_n

        # DGAF-Prompt参数
        self.control_grid_size = config.control_grid_size
        self.summary_blocks = config.summary_blocks

        # GraTa参数
        self.grata_beta = config.grata_beta
        self.consistency_weight = config.consistency_weight

        # Initialize the pre-trained model and optimizer
        self.build_model()

        # Memory Bank
        self.neighbor = config.neighbor
        self.image_summary = ImageSummary(summary_blocks=self.summary_blocks)
        summary_dim = self.image_summary.get_summary_dimension()
        self.memory_bank = Memory(size=config.memory_size, dimension=summary_dim)

        # Print Information
        print("[DEBUG] [INIT] Configuration parameters:")
        for arg, value in vars(config).items():
            print(f"[DEBUG] [INIT] {arg}: {value}")
        self.print_prompt()
        print("[DEBUG] [INIT] " + '***' * 20)

    def build_model(self):
        self.prompt = DGAFPrompt(grid_size=self.control_grid_size, image_size=self.image_size, device=self.device).to(self.device)
        self.model = ResUnet(resnet=self.backbone, num_classes=self.out_ch, pretrained=False, newBN=AdaBN, warm_n=self.warm_n).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        self.model.load_state_dict(checkpoint, strict=True)

        # 初始化数据增强管道
        self.augmentation = create_augmentation_pipeline()

        # 构建GraTa优化器
        prompt_params = list(self.prompt.parameters())
        if self.optim == 'SGD':
            base_optimizer = torch.optim.SGD(
                prompt_params,
                lr=self.lr,
                momentum=self.momentum,
                nesterov=True,
                weight_decay=self.weight_decay
            )
        elif self.optim == 'Adam':
            base_optimizer = torch.optim.Adam(
                prompt_params,
                lr=self.lr,
                betas=self.betas,
                weight_decay=self.weight_decay
            )
        else:
            raise NotImplementedError("ERROR: no such optimizer {}!".format(self.optim))

        self.optimizer = GraTaOptimizer(
            prompt_params=prompt_params,
            base_optimizer=base_optimizer,
            model=self.model,
            device=self.device,
            grata_beta=self.grata_beta
        )



    def print_prompt(self):
        num_params = 0
        for p in self.prompt.parameters():
            num_params += p.numel()
        print("[DEBUG] [INIT] The number of total DGAF-Prompt parameters: {}".format(num_params))

        # 验证参数结构和优化器包含情况
        print("[DEBUG] [INIT] Parameter breakdown:")
        print(f"[DEBUG] [INIT]   delta_p (displacement): {self.prompt.delta_p.numel()} params")
        print(f"[DEBUG] [INIT]   delta_a (appearance): {self.prompt.delta_a.numel()} params")
        print(f"[DEBUG] [INIT]   radius: {self.prompt.radius.numel()} params")
        print(f"[DEBUG] [INIT]   alpha: {self.prompt.alpha.numel()} params")
        expected = self.control_grid_size * self.control_grid_size * 7  # 16*16*7 = 1792
        print(f"[DEBUG] [INIT]   Expected total: {expected} params")
        assert num_params == expected, f"Parameter count mismatch: {num_params} != {expected}"

        # 检查优化器参数
        print("[DEBUG] [INIT] Optimizer parameter groups:")
        for i, param_group in enumerate(self.optimizer.param_groups):
            print(f"[DEBUG] [INIT]   Group {i}: {len(param_group['params'])} parameters")
            for j, param in enumerate(param_group['params']):
                print(f"[DEBUG] [INIT]     Param {j}: shape={param.shape}, requires_grad={param.requires_grad}")

        # 验证每个参数是否在优化器中
        optimizer_params = set()
        for param_group in self.optimizer.param_groups:
            for param in param_group['params']:
                optimizer_params.add(id(param))

        print("[DEBUG] [INIT] Parameter inclusion in optimizer:")
        for name, param in self.prompt.named_parameters():
            in_optimizer = id(param) in optimizer_params
            print(f"[DEBUG] [INIT]   {name}: in_optimizer={in_optimizer}, requires_grad={param.requires_grad}")

    def run(self):
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        print(f"[DEBUG] [TRAINING] Starting VPTTA training on {len(self.target_test_loader)} batches")
        print(f"[DEBUG] [TRAINING] Memory bank size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")
        print(f"[DEBUG] [TRAINING] Using {self.neighbor} neighbors for initialization")

        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = Variable(x).to(self.device), Variable(y).to(self.device)

            self.model.eval()
            self.prompt.train()
            self.model.change_BN_status(new_sample=True)

            # Initialize Prompt
            if len(self.memory_bank.memory.keys()) >= self.neighbor:
                # 生成图像摘要用于检索
                image_summary = self.image_summary.generate_summary(x)
                init_data, score = self.memory_bank.get_neighbours(keys=image_summary.cpu().numpy(), k=self.neighbor)
                print(f"[DEBUG] [MEMORY] Batch {batch}: Retrieved from memory bank, avg_score={score.mean():.4f}")
                self.prompt.update(init_data)
            else:
                # 默认初始化：使用合理的随机值而不是全零
                num_points = self.control_grid_size * self.control_grid_size
                # 创建合理的初始化数据
                init_data = torch.zeros((1, num_points, 7))
                # 位移参数：增加初始化幅度以激活物理约束
                init_data[0, :, :2] = torch.randn(num_points, 2) * 0.05
                # 外观参数：小的随机值
                init_data[0, :, 2:5] = torch.randn(num_points, 3) * 0.01
                # 半径参数：合理的范围
                init_data[0, :, 5] = torch.empty(num_points).uniform_(0.3, 0.8)
                # alpha参数：接近1的值
                init_data[0, :, 6] = torch.empty(num_points).uniform_(0.8, 1.2)

                print(f"[DEBUG] [MEMORY] Batch {batch}: Using reasonable random initialization (memory bank size: {len(self.memory_bank.memory.keys())})")
                self.prompt.update(init_data)

            # GraTa优化 (单次优化)
            self.model.train()
            self.prompt.train()

            # 执行GraTa优化步骤
            loss_info = self.optimizer.step(x, self.prompt, self.augmentation, self.consistency_weight)

            # 输出损失详情
            print(f"[DEBUG] [GRATA] Batch {batch}:")
            print(f"[DEBUG] [GRATA]   BN Loss: {loss_info['bn_loss']:.6f}")
            print(f"[DEBUG] [GRATA]   Consistency Loss: {loss_info['consistency_loss']:.6f}")
            print(f"[DEBUG] [GRATA]   Cosine Similarity: {loss_info['cosine_similarity']:.6f}")
            print(f"[DEBUG] [GRATA]   LR Scale: {loss_info['lr_scale']:.6f}")
            print(f"[DEBUG] [GRATA]   Current LR: {loss_info['current_lr']:.8f}")

            self.model.change_BN_status(new_sample=False)

            # Inference
            self.model.eval()
            self.prompt.eval()
            with torch.no_grad():
                prompt_x, image_summary = self.prompt(x)
                pred_logit, _, _ = self.model(prompt_x)

            # Update the Memory Bank
            # 获取当前优化后的控制点参数，组装为[256, 7]格式
            control_points = torch.cat([
                self.prompt.delta_p,      # [256, 2]
                self.prompt.delta_a,      # [256, 3]
                self.prompt.radius.unsqueeze(1),  # [256, 1]
                self.prompt.alpha.unsqueeze(1)    # [256, 1]
            ], dim=1).unsqueeze(0).detach().cpu().numpy()  # [1, 256, 7]

            self.memory_bank.push(keys=image_summary.cpu().numpy(), control_points=control_points)
            print(f"[DEBUG] [MEMORY] Batch {batch}: Updated memory bank, current size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")

            # Calculate the metrics
            seg_output = torch.sigmoid(pred_logit)
            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())

            # 输出当前batch的指标
            self._log_metrics(batch, metrics)

            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

        test_metrics_y = np.mean(metrics_test, axis=1)
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]

        print("[DEBUG] [FINAL] " + "="*50)
        print("[DEBUG] [FINAL] VPTTA Training Completed")
        print("[DEBUG] [FINAL] Final Test Metrics:")
        for metric_name, value in print_test_metric_mean.items():
            print(f"[DEBUG] [FINAL]   {metric_name}: {value:.4f}")
        mean_dice = (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2
        print(f"[DEBUG] [FINAL]   Mean Dice: {mean_dice:.4f}")
        print(f"[DEBUG] [FINAL] Final Memory Bank Size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")
        print("[DEBUG] [FINAL] " + "="*50)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Optimizer
    parser.add_argument('--optimizer', type=str, default='Adam', help='SGD/Adam')
    parser.add_argument('--lr', type=float, default=0.05)
    parser.add_argument('--momentum', type=float, default=0.99)  # momentum in SGD
    parser.add_argument('--beta1', type=float, default=0.9)      # beta1 in Adam
    parser.add_argument('--beta2', type=float, default=0.99)     # beta2 in Adam.
    parser.add_argument('--weight_decay', type=float, default=0.00)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)

    # Hyperparameters in memory bank and warm-up statistics
    parser.add_argument('--memory_size', type=int, default=40)
    parser.add_argument('--neighbor', type=int, default=16)
    parser.add_argument('--warm_n', type=int, default=5)

    # DGAF-Prompt parameters
    parser.add_argument('--control_grid_size', type=int, default=16)
    parser.add_argument('--summary_blocks', type=int, default=8)

    # GraTa parameters
    parser.add_argument('--grata_beta', type=float, default=0.25)
    parser.add_argument('--consistency_weight', type=float, default=1.0)

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = VPTTA(config)
    TTA.run()
