#!/usr/bin/env python3
"""
测试GraTa优化器功能
"""
import torch
import torch.nn as nn

print("Starting imports...")

try:
    from utils.grata_optimizer import GraTaOptimizer
    print("✅ GraTa optimizer imported")
except Exception as e:
    print(f"❌ Error importing GraTa optimizer: {e}")
    exit(1)

try:
    from utils.augmentation import create_augmentation_pipeline
    print("✅ Augmentation pipeline imported")
except Exception as e:
    print(f"❌ Error importing augmentation: {e}")
    exit(1)

try:
    from utils.dgaf_prompt import DGAFPrompt
    print("✅ DGAF prompt imported")
except Exception as e:
    print(f"❌ Error importing DGAF prompt: {e}")
    exit(1)

try:
    from utils.convert import AdaBN
    print("✅ AdaBN imported")
except Exception as e:
    print(f"❌ Error importing AdaBN: {e}")
    exit(1)

try:
    from networks.ResUnet_TTA import ResUnet
    print("✅ ResUnet imported")
except Exception as e:
    print(f"❌ Error importing ResUnet: {e}")
    exit(1)

print("All imports successful!")

def test_grata_optimizer():
    """测试GraTa优化器的基本功能"""
    device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # 创建模拟的prompt和模型
    prompt = DGAFPrompt(grid_size=4, image_size=128, device=device).to(device)
    model = ResUnet(resnet='resnet34', num_classes=2, pretrained=False, newBN=AdaBN, warm_n=5).to(device)
    
    # 创建优化器
    prompt_params = list(prompt.parameters())
    base_optimizer = torch.optim.Adam(prompt_params, lr=0.001)
    grata_optimizer = GraTaOptimizer(
        prompt_params=prompt_params,
        base_optimizer=base_optimizer,
        model=model,
        device=device,
        grata_beta=0.25
    )
    
    # 创建数据增强管道
    augmentation = create_augmentation_pipeline()
    
    # 创建测试数据
    x = torch.randn(1, 3, 128, 128).to(device)
    
    print("Testing virtual update and restore...")
    
    # 保存原始参数
    original_params = {}
    for i, p in enumerate(prompt_params):
        original_params[i] = p.data.clone()
    
    # 计算BN损失以获得梯度
    bn_loss = grata_optimizer.calculate_bn_loss(x, prompt)
    print(f"BN Loss: {bn_loss.item():.6f}")
    
    # 执行虚拟更新
    grata_optimizer.perturb_weights_sub()
    
    # 检查参数是否改变
    params_changed = False
    for i, p in enumerate(prompt_params):
        if not torch.equal(p.data, original_params[i]):
            params_changed = True
            break
    
    print(f"Parameters changed after virtual update: {params_changed}")
    
    # 恢复参数
    grata_optimizer.unperturb()
    
    # 检查参数是否恢复
    params_restored = True
    for i, p in enumerate(prompt_params):
        if not torch.equal(p.data, original_params[i]):
            params_restored = False
            break
    
    print(f"Parameters restored after unperturb: {params_restored}")
    
    # 测试完整的GraTa步骤
    print("\nTesting full GraTa step...")
    try:
        loss_info = grata_optimizer.step(x, prompt, augmentation, consistency_weight=1.0)
        print("GraTa step completed successfully!")
        print(f"Loss info: {loss_info}")
        return True
    except Exception as e:
        print(f"Error in GraTa step: {e}")
        return False

def test_augmentation():
    """测试数据增强功能"""
    print("\nTesting augmentation pipeline...")
    
    # 创建数据增强管道
    augmentation = create_augmentation_pipeline()
    
    # 创建测试数据
    x = torch.randn(2, 3, 128, 128)
    
    # 测试增强
    x_weak, x_strong, factors = augmentation.generate_augmented_samples(x)
    
    print(f"Original shape: {x.shape}")
    print(f"Weak augmented shape: {x_weak.shape}")
    print(f"Strong augmented shape: {x_strong.shape}")
    print(f"Inverse factors: {factors}")
    
    # 测试逆变换
    pred = torch.randn(2, 2, 128, 128)  # 模拟预测结果
    pred_restored = augmentation.inverse_weak_augmentation(pred, factors)
    print(f"Prediction restored shape: {pred_restored.shape}")
    
    print("Augmentation test passed!")
    return True

if __name__ == "__main__":
    print("Starting GraTa integration tests...")
    
    # 测试数据增强
    aug_success = test_augmentation()
    
    # 测试GraTa优化器
    grata_success = test_grata_optimizer()
    
    if aug_success and grata_success:
        print("\n✅ All tests passed! GraTa integration is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
