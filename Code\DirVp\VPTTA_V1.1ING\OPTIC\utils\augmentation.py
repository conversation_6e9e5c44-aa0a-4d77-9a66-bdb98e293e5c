import torch
import torch.nn as nn
import random
import numpy as np


class WeakAugmentation(nn.Module):
    """弱增强：翻转和旋转变换"""
    
    def __init__(self):
        super().__init__()
    
    def __call__(self, x):
        """
        对输入图像进行弱增强
        Args:
            x: 输入图像 [B, 3, H, W]
        Returns:
            augmented_x: 增强后图像
            factors: 变换因子列表，用于逆变换
        """
        factors = []
        augmented_x = x.clone()
        
        # 随机选择变换因子 (0-4: 翻转和旋转, 5: 无变换)
        for b in range(x.shape[0]):
            factor = random.randint(0, 4)
            factors.append(factor)
            augmented_x[b] = self._apply_transform(x[b], factor)
        
        return augmented_x, factors
    
    def _apply_transform(self, x, factor):
        """应用单个变换"""
        if factor == 0:
            return x.flip(-1)  # 水平翻转
        elif factor == 1:
            return x.flip(-2)  # 垂直翻转
        elif factor == 2:
            return x.flip(-1).transpose(-2, -1)  # 90度旋转
        elif factor == 3:
            return x.flip(-1).flip(-2)  # 180度旋转
        elif factor == 4:
            return x.transpose(-2, -1).flip(-1)  # 270度旋转
        else:
            return x  # 无变换
    
    def inverse(self, pred, factors):
        """
        对预测结果进行逆变换
        Args:
            pred: 预测结果 [B, C, H, W]
            factors: 变换因子列表
        Returns:
            inverse_pred: 逆变换后的预测结果
        """
        inverse_pred = pred.clone()
        
        for b, factor in enumerate(factors):
            inverse_pred[b] = self._apply_inverse_transform(pred[b], factor)
        
        return inverse_pred
    
    def _apply_inverse_transform(self, pred, factor):
        """应用逆变换"""
        if factor == 0:
            return pred.flip(-1)  # 水平翻转
        elif factor == 1:
            return pred.flip(-2)  # 垂直翻转
        elif factor == 2:
            return pred.transpose(-2, -1).flip(-1)  # 90度逆旋转
        elif factor == 3:
            return pred.flip(-1).flip(-2)  # 180度逆旋转
        elif factor == 4:
            return pred.flip(-1).transpose(-2, -1)  # 270度逆旋转
        else:
            return pred  # 无变换


class StrongAugmentation(nn.Module):
    """强增强：亮度、对比度、噪声变换"""
    
    def __init__(self):
        super().__init__()
    
    def __call__(self, x):
        """
        对输入图像进行强增强
        Args:
            x: 输入图像 [B, 3, H, W]
        Returns:
            augmented_x: 增强后图像
        """
        augmented_x = x.clone()
        
        for b in range(x.shape[0]):
            # 亮度调整 (0.5-1.5)
            if random.random() < 0.75:
                brightness_factor = random.uniform(0.5, 1.5)
                augmented_x[b] = augmented_x[b] * brightness_factor
            
            # 对比度调整 (0.5-1.5)
            if random.random() < 0.75:
                contrast_factor = random.uniform(0.5, 1.5)
                mean_val = augmented_x[b].mean(dim=(-2, -1), keepdim=True)
                augmented_x[b] = (augmented_x[b] - mean_val) * contrast_factor + mean_val
            
            # 高斯噪声 (std=0.05)
            if random.random() < 0.5:
                noise = torch.randn_like(augmented_x[b]) * 0.05
                augmented_x[b] = augmented_x[b] + noise
        
        # 确保像素值在合理范围内
        augmented_x = torch.clamp(augmented_x, 0.0, 1.0)
        
        return augmented_x


class AugmentationPipeline:
    """数据增强管道"""
    
    def __init__(self):
        self.weak_aug = WeakAugmentation()
        self.strong_aug = StrongAugmentation()
    
    def generate_augmented_samples(self, x):
        """
        生成弱增强和强增强样本
        Args:
            x: 原始输入图像 [B, 3, H, W]
        Returns:
            x_weak: 弱增强图像
            x_strong: 强增强图像
            inverse_factors: 弱增强的逆变换因子
        """
        # 生成弱增强样本
        x_weak, inverse_factors = self.weak_aug(x)
        
        # 生成强增强样本
        x_strong = self.strong_aug(x)
        
        return x_weak, x_strong, inverse_factors
    
    def inverse_weak_augmentation(self, pred, inverse_factors):
        """对弱增强预测结果进行逆变换"""
        return self.weak_aug.inverse(pred, inverse_factors)


def create_augmentation_pipeline():
    """创建数据增强管道的工厂函数"""
    return AugmentationPipeline()
