#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/

#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REF<PERSON>GE_Valid, Drishti_GS]
Source=Drishti_GS

#Optimizer
optimizer=Adam
lr=0.005

#Hyperparameters
memory_size=40
neighbor=16
warm_n=5

#DGAF-Prompt参数
control_grid_size=16
summary_blocks=8

#GraTa相关参数
grata_beta=0.25
consistency_weight=1.0

#Command
cd OPTIC
CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--optimizer $optimizer --lr $lr \
--memory_size $memory_size --neighbor $neighbor --warm_n $warm_n \
--control_grid_size $control_grid_size --summary_blocks $summary_blocks \
--grata_beta $grata_beta --consistency_weight $consistency_weight