import torch
import torch.nn as nn


class GraTaOptimizer:
    def __init__(self, prompt_params, base_optimizer, model, device, grata_beta=0.25):
        """
        GraTa优化器实现
        Args:
            prompt_params: prompt参数列表
            base_optimizer: 基础优化器(Adam/SGD)
            model: ResUnet模型
            device: 设备
            grata_beta: 动态学习率缩放因子
        """
        self.prompt_params = prompt_params
        self.base_optimizer = base_optimizer
        self.model = model
        self.device = device
        self.grata_beta = grata_beta
        self.init_lr = base_optimizer.param_groups[0]['lr']
        
        # 存储参数状态
        self.param_states = {}
        
    def perturb_weights_sub(self):
        """虚拟更新：用辅助梯度对参数进行虚拟更新"""
        for p in self.prompt_params:
            if p.grad is None:
                continue
            # 保存原始参数
            self.param_states[p] = {
                'old_p': p.data.clone(),
                'aux_g': p.grad.data.clone()
            }
            # 执行虚拟更新: θ_p' = θ_p - ∇BN_loss
            p.data.sub_(p.grad.data)
    
    def unperturb(self):
        """恢复参数到虚拟更新前的状态"""
        for p in self.prompt_params:
            if p in self.param_states:
                p.data.copy_(self.param_states[p]['old_p'])
    
    def calculate_bn_loss(self, x, prompt):
        """
        计算BN损失作为辅助梯度
        Args:
            x: 输入图像 [B, 3, H, W]
            prompt: DGAF-Prompt模块
        Returns:
            bn_loss: BN统计量对齐损失
        """
        self.base_optimizer.zero_grad()
        
        # 通过prompt变换图像
        prompt_x, _ = prompt(x)
        
        # 前向传播获取BN损失
        self.model(prompt_x)
        
        # 计算BN损失
        from utils.convert import AdaBN
        times, bn_loss = 0, 0
        for nm, m in self.model.named_modules():
            if isinstance(m, AdaBN):
                bn_loss += m.bn_loss
                times += 1
        bn_loss = bn_loss / times if times > 0 else torch.tensor(0.0, device=self.device)
        
        # 反向传播计算辅助梯度
        bn_loss.backward()
        
        return bn_loss
    
    def calculate_consistency_loss(self, x_weak, x_strong, prompt, consistency_weight=1.0):
        """
        计算一致性损失作为伪梯度
        Args:
            x_weak: 弱增强图像 [B, 3, H, W]
            x_strong: 强增强图像 [B, 3, H, W]
            prompt: DGAF-Prompt模块
            consistency_weight: 一致性损失权重
        Returns:
            consistency_loss: 一致性损失
        """
        self.base_optimizer.zero_grad()
        
        # 对弱增强图像进行预测（无梯度）
        with torch.no_grad():
            prompt_x_weak, _ = prompt(x_weak)
            pred_weak, _, _ = self.model(prompt_x_weak)
            pred_weak = torch.sigmoid(pred_weak)
        
        # 对强增强图像进行预测（需要梯度）
        prompt_x_strong, _ = prompt(x_strong)
        pred_strong, _, _ = self.model(prompt_x_strong)
        
        # 计算一致性损失
        consistency_loss = nn.BCEWithLogitsLoss()(pred_strong, pred_weak) * consistency_weight
        
        # 反向传播计算伪梯度
        consistency_loss.backward()
        
        return consistency_loss
    
    def get_cosine_similarity(self):
        """计算辅助梯度和伪梯度的余弦相似度"""
        inner_prod = 0.0
        aux_grad_norm = 0.0
        pse_grad_norm = 0.0
        
        for p in self.prompt_params:
            if p.grad is None or p not in self.param_states:
                continue
                
            aux_g = self.param_states[p]['aux_g']
            pse_g = p.grad.data
            
            # 计算内积
            inner_prod += torch.sum(aux_g * pse_g)
            
            # 计算范数
            aux_grad_norm += torch.sum(aux_g ** 2)
            pse_grad_norm += torch.sum(pse_g ** 2)
        
        aux_grad_norm = torch.sqrt(aux_grad_norm)
        pse_grad_norm = torch.sqrt(pse_grad_norm)
        
        # 计算余弦相似度
        cosine = inner_prod / (aux_grad_norm * pse_grad_norm + 1e-12)
        
        return cosine.detach()
    
    def custom_activation(self, cosine):
        """
        GraTa的动态学习率激活函数
        Args:
            cosine: 余弦相似度
        Returns:
            动态学习率缩放因子
        """
        return self.grata_beta * (1.0 / 4.0) * ((cosine + 1) ** 2)
    
    def step(self, x, prompt, augmentation, consistency_weight=1.0):
        """
        执行一次GraTa优化步骤
        Args:
            x: 原始输入图像 [B, 3, H, W]
            prompt: DGAF-Prompt模块
            augmentation: 数据增强模块
            consistency_weight: 一致性损失权重
        """
        # 生成增强样本
        x_weak, x_strong, inverse_factors = augmentation.generate_augmented_samples(x)
        
        # 步骤1: 计算辅助梯度 (BN损失)
        bn_loss = self.calculate_bn_loss(x, prompt)
        
        # 步骤2: 虚拟更新
        self.perturb_weights_sub()
        
        # 步骤3: 计算伪梯度 (一致性损失)
        consistency_loss = self.calculate_consistency_loss(x_weak, x_strong, prompt, consistency_weight)
        
        # 步骤4: 计算余弦相似度
        cosine = self.get_cosine_similarity()
        
        # 步骤5: 恢复参数
        self.unperturb()
        
        # 步骤6: 计算动态学习率
        lr_scale = self.custom_activation(cosine)
        self.base_optimizer.param_groups[0]['lr'] = self.init_lr * lr_scale
        
        # 步骤7: 执行真实更新
        self.base_optimizer.step()
        
        return {
            'bn_loss': bn_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'cosine_similarity': cosine.item(),
            'lr_scale': lr_scale.item(),
            'current_lr': self.base_optimizer.param_groups[0]['lr']
        }
