print("Testing GraTa integration...")

import torch
from utils.grata_optimizer import GraTaOptimizer
from utils.augmentation import create_augmentation_pipeline

print("✅ All imports successful")

# 测试数据增强
print("\n🧪 Testing augmentation pipeline...")
x = torch.randn(2, 3, 128, 128)
aug_pipeline = create_augmentation_pipeline()
x_weak, x_strong, factors = aug_pipeline.generate_augmented_samples(x)

print(f"Original shape: {x.shape}")
print(f"Weak augmented shape: {x_weak.shape}")
print(f"Strong augmented shape: {x_strong.shape}")
print(f"Inverse factors: {factors}")
print("✅ Augmentation test passed!")

# 测试GraTa优化器创建
print("\n🧪 Testing GraTa optimizer creation...")
device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")

# 创建简单的参数用于测试
test_params = [torch.randn(10, 10, requires_grad=True, device=device)]
base_optimizer = torch.optim.Adam(test_params, lr=0.001)

# 创建模拟模型
class MockModel(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = torch.nn.Linear(10, 10)

    def forward(self, x):
        return self.linear(x)

    def named_modules(self):
        return []

mock_model = MockModel().to(device)

grata_optimizer = GraTaOptimizer(
    prompt_params=test_params,
    base_optimizer=base_optimizer,
    model=mock_model,
    device=device,
    grata_beta=0.25
)

print("✅ GraTa optimizer created successfully!")

print("\n🎉 All tests passed! GraTa integration is working correctly.")
